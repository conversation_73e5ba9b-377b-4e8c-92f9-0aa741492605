<template>
  <div
    class="px-4 py-8 border-t border-gray-100 lg:border-t-0 lg:border-l bg-gray-50 lg:px-8 lg:py-12 xl:pl-12 flex flex-col gap-5"
  >
    <div v-if="pendingPhotos.length > 0" class="px-4 py-5 bg-gray-100 border border-gray-200 rounded-lg">
      <div class="flex items-center gap-4">
        <svg
          class="w-5 h-5 text-gray-500 animate-spin"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          />
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
        <p class="text-sm font-medium text-gray-600">
          {{ $t('Checking your photos, hang on!') }}
        </p>
      </div>
      <div class="grid grid-cols-5 md:grid-cols-6 gap-2.5 mt-4 lg:pr-20 xl:pr-32">
        <PostcheckoutUploadedPhoto v-for="photo in pendingPhotos" :key="photo.md5 + '_' + photo.status" :photo="photo" />
      </div>
    </div>

    <template v-if="goodPhotos.length > 0 || pendingPhotos.length > 0">
      <div class="flex items-center justify-between lg:hidden">
        <p class="text-base font-bold tracking-tight text-primary-500">
          {{ $t('Review photos') }}
        </p>

        <PostcheckoutProgressIndicator :current="goodPhotos.length" :total="minimumPhotos" />
      </div>
      <!-- FACE PHOTOS -->
      <div
        class="px-4 py-5 border rounded-lg transition-all duration-200 relative"
        :class="faceDropZoneActive ?
          'border-sky-500 border-dashed' :
          'border-green-200 bg-green-50'"
        @dragover.prevent="onDragOver('face')"
        @dragenter.prevent="onDragEnter('face')"
        @dragleave.prevent="onDragLeave('face')"
        @drop.prevent="onDrop('face', $event)"
      >
        <!-- Drop overlay -->
        <div
          v-if="faceDropZoneActive"
          class="absolute inset-0 bg-sky-50 rounded-lg flex items-center justify-center z-10"
        >
          <p class="text-primary-500 tracking-tight font-medium text-center">
            {{ $t('Drop here to move to face photos') }}
          </p>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center justify-start gap-2">
            <p class="text-sm font-medium tracking-tight text-green-800">
              {{ $t('Good face photos') }}
            </p>
            <Tooltip v-if="goodFacePhotos.length > (minimumPhotos - minimumFullBodyPhotos)" :info="`You've uploaded more photos than needed. Delete atleast ${goodFacePhotos.length - (minimumPhotos - minimumFullBodyPhotos) } face photos or we will delete randomly for you.`" placement="left">
              <template #icon>
                <IconExclamation class="w-4 h-4 text-orange-500" />
              </template>
            </Tooltip>
          </div>

          <PostcheckoutProgressIndicator class="hidden md:block" :current="goodFacePhotos.length" :total="minimumPhotos" />
        </div>

        <hr class="mt-4 border-green-200">

        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2.5 mt-4 lg:pr-20 xl:pr-32">
          <PostcheckoutUploadedPhoto v-for="photo in goodFacePhotos" :key="photo.md5 + '_' + photo.status" :photo="photo" enable-actions />
        </div>
      </div>
      <!-- END FACE PHOTOS -->
      <!-- FULL BODY PHOTOS -->
      <div
        class="px-4 py-5 border rounded-lg transition-all duration-200 relative"
        :class="fullBodyDropZoneActive ?
          'border-sky-500 border-dashed' :
          'border-green-200 bg-green-50'"
        @dragover.prevent="onDragOver('fullBody')"
        @dragenter.prevent="onDragEnter('fullBody')"
        @dragleave.prevent="onDragLeave('fullBody')"
        @drop.prevent="onDrop('fullBody', $event)"
      >
        <!-- Drop overlay -->
        <div
          v-if="fullBodyDropZoneActive"
          class="absolute inset-0 bg-sky-50 rounded-lg flex items-center justify-center z-10"
        >
          <p class="text-primary-500 tracking-tight font-medium text-center">
            {{ $t('Drop here to move to full body photos') }}
          </p>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center justify-start gap-2">
            <p class="text-sm font-medium tracking-tight text-green-800">
              {{ $t('Good full body photos') }}
            </p>
          </div>

          <PostcheckoutProgressIndicator class="hidden md:block" :current="goodFullBodyPhotos.length" :total="minimumFullBodyPhotos" />
        </div>

        <hr class="mt-4 border-green-200">

        <div v-if="goodFullBodyPhotos.length > 0" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2.5 mt-4 lg:pr-20 xl:pr-32">
          <PostcheckoutUploadedPhoto v-for="photo in goodFullBodyPhotos" :key="photo.md5 + '_' + photo.status" :photo="photo" draggable />
        </div>
        <span v-else>
          <p class="text-sm text-gray-500 py-2">
            {{ $t('Full body photos are optional, but we recommend uploading at least two for higher quality headshots.') }}
          </p>
        </span>
      </div>
      <!-- END FULL BODY PHOTOS -->
    </template>

    <div v-if="badPhotos.length > 0" class="px-4 py-5 border border-red-200 rounded-lg bg-red-50">
      <div class="flex items-center justify-between">
        <p class="text-sm font-medium tracking-tight text-red-800">
          {{ $t('Bad photos') }}
        </p>

        <button type="button" class="text-sm font-medium text-red-800 underline" @click="openRequirementsModal">
          {{ $t('Show photo requirements') }}
        </button>
      </div>
      <p class="mt-1 text-sm font-normal text-red-800">
        {{ $t('We don\'t think these photos will result in headshots you\'d be happy with.') }}
      </p>

      <hr class="mt-4 border-red-200">

      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2.5 mt-4 lg:pr-20 xl:pr-32">
        <PostcheckoutUploadedPhoto v-for="photo in badPhotos" :key="photo.md5 + '_' + photo.status" :photo="photo" />
      </div>
    </div>
  </div>
</template>

<script>
import PostcheckoutMixin from '../../mixins/PostcheckoutMixin'
export default {
  mixins: [PostcheckoutMixin],
  data () {
    return {
      faceDropZoneActive: false,
      fullBodyDropZoneActive: false,
      dragCounter: { face: 0, fullBody: 0 }
    }
  },
  methods: {
    onDragOver (zone) {
      // Required to allow drop
    },
    onDragEnter (zone) {
      this.dragCounter[zone]++
      if (zone === 'face') {
        this.faceDropZoneActive = true
      } else if (zone === 'fullBody') {
        this.fullBodyDropZoneActive = true
      }
    },
    onDragLeave (zone) {
      this.dragCounter[zone]--
      if (this.dragCounter[zone] === 0) {
        if (zone === 'face') {
          this.faceDropZoneActive = false
        } else if (zone === 'fullBody') {
          this.fullBodyDropZoneActive = false
        }
      }
    },
    onDrop (targetZone, event) {
      // Reset drop zone states
      this.faceDropZoneActive = false
      this.fullBodyDropZoneActive = false
      this.dragCounter = { face: 0, fullBody: 0 }

      try {
        const data = JSON.parse(event.dataTransfer.getData('text/plain'))
        const { md5, currentCategory } = data

        // Don't do anything if dropping in the same category
        if ((targetZone === 'face' && currentCategory === 'face') ||
            (targetZone === 'fullBody' && currentCategory === 'fullBody')) {
          return
        }

        // Update the photo category
        const newFullBodyValue = targetZone === 'fullBody'
        this.$store.dispatch('onboarding/updatePhotoCategory', {
          md5,
          fullBody: newFullBodyValue
        })
      } catch (error) {
        console.error('Error processing drop:', error)
        this.$toast.error('Failed to move photo')
      }
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Checking your photos, hang on!": "Checking your photos, hang on!",
      "Review photos": "Review photos",
      "Good photos": "Good photos",
      "Good face photos": "Good face photos",
      "Good full body photos": "Good full body photos",
      "Bad photos": "Bad photos",
      "Show photo requirements": "Show photo requirements",
      "We don't think these photos will result in headshots you'd be happy with.": "We don't think these photos will result in headshots you'd be happy with.",
      "Drop here to move to face photos": "Drop here to move to face photos",
      "Drop here to move to full body photos": "Drop here to move to full body photos",
      "Full body photos are optional, but we recommend uploading at least two for higher quality headshots.": "Full body photos are optional, but we recommend uploading at least two for higher quality headshots."
    },
    "es": {
      "Checking your photos, hang on!": "¡Estamos revisando tus fotos, espera!",
      "Review photos": "Revisar fotos",
      "Good photos": "Buenas fotos",
      "Good face photos": "Buenas fotos de rostro",
      "Good full body photos": "Buenas fotos de cuerpo entero",
      "Bad photos": "Malas fotos",
      "Show photo requirements": "Mostrar requisitos para las fotos",
      "We don't think these photos will result in headshots you'd be happy with.": "No creemos que estas fotos resulten en fotos con las que estarías contento.",
      "Drop here to move to face photos": "Suelta aquí para mover a fotos de rostro",
      "Drop here to move to full body photos": "Suelta aquí para mover a fotos de cuerpo entero",
      "Full body photos are optional, but we recommend uploading at least two for higher quality headshots.": "Las fotos de cuerpo entero son opcionales, pero recomendamos subir al menos dos para obtener fotos de mejor calidad."
    },
    "de": {
      "Checking your photos, hang on!": "Wir prüfen deine Fotos, einen Moment!",
      "Review photos": "Fotos überprüfen",
      "Good photos": "Gute Fotos",
      "Good face photos": "Gute Gesichtsfotos",
      "Good full body photos": "Gute Ganzkörperfotos",
      "Bad photos": "Schlechte Fotos",
      "Show photo requirements": "Foto-Anforderungen anzeigen",
      "We don't think these photos will result in headshots you'd be happy with.": "Wir denken nicht, dass diese Fotos zu Bewerbungsfotos führen, mit denen du zufrieden wärst.",
      "Drop here to move to face photos": "Hier ablegen, um zu Gesichtsfotos zu verschieben",
      "Drop here to move to full body photos": "Hier ablegen, um zu Ganzkörperfotos zu verschieben",
      "Full body photos are optional, but we recommend uploading at least two for higher quality headshots.": "Ganzkörperfotos sind optional, aber wir empfehlen, mindestens zwei hochzuladen, um qualitativ hochwertigere Bewerbungsfotos zu erhalten."
    }
  }
</i18n>
