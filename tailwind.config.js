const { iconsPlugin, getIconCollections } = require('@egoist/tailwindcss-icons')
const colors = require('tailwindcss/colors')
const defaultTheme = require('tailwindcss/defaultTheme')
module.exports = {
  content: ['./components/**/*.{js,vue,ts}', './layouts/**/*.vue', './pages/**/*.vue', './plugins/**/*.{js,ts}', './nuxt.config.{js,ts}'],
  theme: {
    fontFamily: {
      sans: ['Plus Jakarta Sans', ...defaultTheme.fontFamily.sans],
      serif: 'Georgia, Cambria, Times New Roman, Times, serif',
      cursive: ['Architects Daughter'] // Added new font family
    },
    screens: {
      '2xs': '380px',
      xs: '580px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
      '3xl': '1800px'
    },
    extend: {
      keyframes: {
        scroll: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-50%)' }
        }
      },
      animation: {
        scroll: 'scroll 20s linear infinite'
      },
      gridTemplateColumns: {
        // Simple 16 column grid
        18: 'repeat(16, minmax(0, 1fr))'
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))'
      },
      screens: {
        '3xl': '1800px'
      },
      minHeight: {
        0: '0',
        '1/4': '25vh',
        '1/2': '50vh',
        '3/4': '75vh',
        screen: '100vh'
      },
      colors: {
        gray: colors.gray,
        muted: {
          50: '#F8FAFC'
        },
        primary: {
          500: '#1B145D',
          600: '#120C4A'
        },
        paragraph: '#6c697f', // Added new color
        paragraphlp: '#474368',
        brand: {
          // 500: "#1B145D",
          DEFAULT: '#1B145D',
          50: '#5D4FD9',
          100: '#4D3FD6',
          200: '#382AC2',
          300: '#2E22A0',
          400: '#251B7F',
          500: '#1B145D',
          600: '#0E0A2F',
          700: '#0E0A2F',
          800: '#0E0A2F',
          900: '#0E0A2F'
        },
        teal: {
          50: '#f4fbfa', // Updated teal colors
          100: '#e9f7f6',
          200: '#c7eae7',
          300: '#a6ded9',
          400: '#63c5bd',
          500: '#20aca0',
          600: '#1d9b90',
          700: '#188178',
          800: '#136760',
          900: '#10544e'
        },
        error: {
          500: '#C54038', // Added new error color
          600: '#AE051A'
        },
        warning: {
          600: '#AE6A05' // Added new warning color
        },
        success: {
          600: '#17A400' // Added new success color
        }
      },
      boxShadow: {
        custom: '0 0px 6px 0px rgba(0, 0, 0, 0.25)',
        box: '0px 0px 75px rgba(0, 0, 0, 0.07)',
        upsell: '0px 0px 24px 0px rgba(0, 0, 0, 0.25)'
      },
      spacing: {
        22: '5.5rem'
      },
      borderWidth: {
        6: '6px'
      },
      maxWidth: {
        '8xl': '1480px'
      },
      screens: {
        '2xl': '1440px', // Added new screen size
        '3xl': '1800px',
        '3xl': '1680px' // Added new screen size
      },
      aspectRatio: {
        result: '270 / 219'
      }
    }

  },
  variants: {
    extend: {
      width: ['hover'],
      background: ['responsive'],
      borderWidth: ['last']
    }
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms')({
      strategy: 'class'
    }),
    require('@tailwindcss/container-queries'),
    iconsPlugin({
      collections: getIconCollections(['heroicons'])
    })
  ]

}
