// utils/breakpoints.js

import Vue from 'vue'

// Extract breakpoint sizes from Tailwind config and convert to numbers
const sizes = {
  '2xs': 380,
  xs: 580,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
  '3xl': 1800
}

const state = Vue.observable({
  width: typeof window !== 'undefined' ? window.innerWidth : 1024 // Default to desktop size for SSR
})

// Only add event listener on client side
if (typeof window !== 'undefined') {
  window.addEventListener('resize', () => {
    state.width = window.innerWidth
  })
}

export const breakpoints = {
  // 2xs breakpoints
  get '2xsAndDown' () { return state.width < sizes.xs },
  get '2xsAndUp' () { return state.width >= sizes['2xs'] },

  // xs breakpoints
  get xsAndDown () { return state.width < sizes.sm },
  get xsAndUp () { return state.width >= sizes.xs },

  // sm breakpoints
  get smAndDown () { return state.width < sizes.md },
  get smAndUp () { return state.width >= sizes.sm },

  // md breakpoints
  get mdAndDown () { return state.width < sizes.lg },
  get mdAndUp () { return state.width >= sizes.md },

  // lg breakpoints
  get lgAndDown () { return state.width < sizes.xl },
  get lgAndUp () { return state.width >= sizes.lg },

  // xl breakpoints
  get xlAndDown () { return state.width < sizes['2xl'] },
  get xlAndUp () { return state.width >= sizes.xl },

  // 2xl breakpoints
  get '2xlAndDown' () { return state.width < sizes['3xl'] },
  get '2xlAndUp' () { return state.width >= sizes['2xl'] },

  // 3xl breakpoints
  get '3xlAndUp' () { return state.width >= sizes['3xl'] },

  get width () {
    return state.width
  },

  // Method to initialize/update width on client side (useful for SSR)
  init () {
    if (typeof window !== 'undefined') {
      state.width = window.innerWidth
    }
  }
}
