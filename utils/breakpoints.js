// utils/breakpoints.js

import Vue from 'vue'
import { screens } from '../tailwind.config'

const sizes = screens

console.log('theme', sizes)

const state = Vue.observable({
  width: window.innerWidth
})

window.addEventListener('resize', () => {
  state.width = window.innerWidth
})

export const breakpoints = {
  // 2xs breakpoints
  get '2xsAndDown' () { return state.width < sizes.xs },
  get '2xsAndUp' () { return state.width >= sizes['2xs'] },

  // xs breakpoints
  get xsAndDown () { return state.width < sizes.sm },
  get xsAndUp () { return state.width >= sizes.xs },

  // sm breakpoints
  get smAndDown () { return state.width < sizes.md },
  get smAndUp () { return state.width >= sizes.sm },

  // md breakpoints
  get mdAndDown () { return state.width < sizes.lg },
  get mdAndUp () { return state.width >= sizes.md },

  // lg breakpoints
  get lgAndDown () { return state.width < sizes.xl },
  get lgAndUp () { return state.width >= sizes.lg },

  // xl breakpoints
  get xlAndDown () { return state.width < sizes['2xl'] },
  get xlAndUp () { return state.width >= sizes.xl },

  // 2xl breakpoints
  get '2xlAndDown' () { return state.width < sizes['3xl'] },
  get '2xlAndUp' () { return state.width >= sizes['2xl'] },

  // 3xl breakpoints
  get '3xlAndUp' () { return state.width >= sizes['3xl'] },

  get width () {
    return state.width
  }
}

// Export the sizes object for direct access to breakpoint values
export const breakpointSizes = sizes
